# yamllint disable rule:line-length
# yamllint disable rule:indentation
# yamllint disable rule:comments-indentation
# yamllint disable rule:comments
# too many spelling things, spell-checker: disable
---
- module-name: '_asyncio' # checksum: 86bf476b
  implicit-imports:
    - depends:
        - 'asyncio'

- module-name: '_curses_panel' # checksum: c9b072ec
  implicit-imports:
    - depends:
        - 'curses'

- module-name: '_elementtree' # checksum: 109ba176
  implicit-imports:
    - depends:
        - 'xml.etree.ElementPath'
        - 'pyexpat'

- module-name: '_osx_support' # checksum: e893ad4b
  anti-bloat:
    - no-auto-follow:
        'distutils': 'ignore'

- module-name: '_sitebuiltins' # checksum: f8cccd39
  anti-bloat:
    - no-auto-follow:
        'pydoc': 'ignore'

- module-name: '_ssl' # checksum: 84c2a8c5
  implicit-imports:
    - depends:
        - 'socket'

- module-name: '_zoneinfo' # checksum: 8de2a2bd
  implicit-imports:
    - depends:
        - 'zoneinfo._common'

- module-name: 'ast' # checksum: 6a44b57d
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'main': "'(lambda: None)'"

- module-name: 'base64' # checksum: dedabd
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'main': "'(lambda: None)'"
        'test': "'(lambda: None)'"

- module-name: 'binhex' # checksum: 167cb032
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'bisect' # checksum: 8255002d
  anti-bloat:
    - description: 'avoid _bisect module usage'
      no-auto-follow:
        '_bisect': 'may slow down by using fallback implementation'
      when: 'not has_builtin_module("_bisect")'

- module-name: 'calendar' # checksum: 6a44b57d
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'main': "'(lambda: None)'"

- module-name: 'copy' # checksum: 167cb032
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'crypt' # checksum: 7d2653dc
  anti-bloat:
    - description: 'avoid _crypt module usage'
      replacements_plain:
        '_crypt module was not built as part of CPython': '_crypt module was not included, used --include-module=_crypt'
      when: 'not has_builtin_module("_crypt")'

- module-name: 'ctypes.util' # checksum: 820d21fd
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'test': "'(lambda: None)'"

- module-name: 'datetime' # checksum: e5cb7fa3
  anti-bloat:
    - description: 'avoid _datetime module usage'
      no-auto-follow:
        '_datetime': 'may slow down by using fallback implementation'
      when: 'not has_builtin_module("_datetime")'

- module-name: 'difflib' # checksum: 167cb032
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'dis' # checksum: 167cb032
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'doctest' # checksum: 167cb032
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'email.utils' # checksum: 58ebf00f
  anti-bloat:
    - description: 'avoid socket module usage'
      replacements_plain:
        'import socket': ''
        'socket.': '__import__("socket").'
      no-auto-follow:
        'socket': "can break calls of 'email.utils.make_msgid()'"
      when: 'not has_builtin_module("_socket")'

- module-name: 'ensurepip' # checksum: fe3524b2
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_main': "'(lambda: None)'"

- module-name: 'ensurepip._uninstall' # checksum: fe3524b2
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_main': "'(lambda: None)'"

- module-name: 'enum' # checksum: a9e2240c
  anti-bloat:
    - replacements_plain:
        'def __call__(cls, value,': 'def __call__(cls, value=None,'
        'return cls.__new__(cls, value)': 'return cls.__new__(cls, value if value is not None or cls.__module__ != "PySide6.QtCore" else 0)'
      when: 'plugin("pyside6")'

- module-name: 'formatter' # checksum: 820d21fd
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'test': "'(lambda: None)'"

- module-name: 'ftplib' # checksum: 820d21fd
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'test': "'(lambda: None)'"

- module-name: 'gzip' # checksum: 5428c614
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_test': "'(lambda: None)'"
        'main': "'(lambda: None)'"

- module-name: 'heapq' # checksum: 406ad100
  anti-bloat:
    - description: 'avoid _heapq module usage'
      no-auto-follow:
        '_datetime': 'may slow down by using fallback implementation'
      when: 'not has_builtin_module("_heapq")'

- module-name: 'imghdr' # checksum: 820d21fd
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'test': "'(lambda: None)'"

- module-name: 'inspect' # checksum: fe3524b2
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_main': "'(lambda: None)'"

- module-name: 'json.decoder' # checksum: f0af106d
  anti-bloat:
    - description: 'avoid _json module usage'
      no-auto-follow:
        '_json': 'may slow down by using fallback implementation'
      when: 'not has_builtin_module("_json")'

- module-name: 'json.encoder' # checksum: f0af106d
  anti-bloat:
    - description: 'avoid _json module usage'
      no-auto-follow:
        '_json': 'may slow down by using fallback implementation'
      when: 'not has_builtin_module("_json")'

- module-name: 'json.scanner' # checksum: f0af106d
  anti-bloat:
    - description: 'avoid _json module usage'
      no-auto-follow:
        '_json': 'may slow down by using fallback implementation'
      when: 'not has_builtin_module("_json")'

- module-name: 'keyword' # checksum: 6a44b57d
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'main': "'(lambda: None)'"

- module-name: 'lib2to3.pgen2' # checksum: 34ec8d8f
  data-files:
    - patterns:
        - '../*.pickle'
      when: 'not debian_python'

- module-name: 'lib2to3.pgen2.literals' # checksum: 820d21fd
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'test': "'(lambda: None)'"

- module-name: 'lib2to3.refactor' # checksum: e884a866
  implicit-imports:
    - depends:
        - 'lib2to3.fixes.*'

- module-name: 'locale' # checksum: 167cb032
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'mailbox' # checksum: 2dd8f89e
  anti-bloat:
    - description: 'avoid fcntl module usage'
      no-auto-follow:
        'fcntl': 'may not lock mailbox files properly using fallback implementation'
      when: 'not has_builtin_module("fcntl")'

- module-name: 'mailcap' # checksum: 820d21fd
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'test': "'(lambda: None)'"

- module-name: 'mimetypes' # checksum: fe3524b2
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_main': "'(lambda: None)'"

- module-name: 'multiprocessing' # checksum: 96475306
  anti-bloat:
    - description: "workaround for 'sys.frozen' not being set"
      replacements_plain:
        "getattr(sys, 'frozen', False)": 'True'

- module-name: 'multiprocessing.context' # checksum: 96475306
  anti-bloat:
    - description: "workaround for 'sys.frozen' not being set"
      replacements_plain:
        "getattr(sys, 'frozen', False)": 'True'

- module-name: 'multiprocessing.forking' # checksum: 96475306
  anti-bloat:
    - description: "workaround for 'sys.frozen' not being set"
      replacements_plain:
        "getattr(sys, 'frozen', False)": 'True'

- module-name: 'multiprocessing.popen_spawn_win32' # checksum: 96475306
  anti-bloat:
    - description: "workaround for 'sys.frozen' not being set"
      replacements_plain:
        "getattr(sys, 'frozen', False)": 'True'

- module-name: 'multiprocessing.resource_tracker' # checksum: e8bba98a
  anti-bloat:
    - description: "workaround for starting 'multiprocessing resource tracker"
      replacements_plain:
        "args += ['-c', cmd % r]": "args += ['--multiprocessing-resource-tracker', str(r)]"
      when: 'not module_mode'

- module-name: 'multiprocessing.spawn' # checksum: 96475306
  anti-bloat:
    - description: "workaround for 'sys.frozen' not being set"
      replacements_plain:
        "getattr(sys, 'frozen', False)": 'True'

- module-name: 'opcode' # checksum: bef1771d
  implicit-imports:
    # Overcome default non-inclusion of this module, required now
    - depends:
        - '_opcode'
      when: 'python313_or_higher'

- module-name: 'pdb' # checksum: 447bd07e
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'test': "'(lambda: None)'"
        'help': "'(lambda: None)'"

- module-name: 'pickle' # checksum: 167cb032
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'pickletools' # checksum: 167cb032
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'platform' # checksum: 2852471a
  anti-bloat:
    - description: 'avoid using plistlib dependency on non-macOS'
      replacements_plain:
        'import plistlib': 'return None'
      when: 'not macos'

- module-name: 'pyclbr' # checksum: fe3524b2
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_main': "'(lambda: None)'"

- module-name: 'pydoc' # checksum: 408973d4
  anti-bloat:
    - description: 'remove module ability to display GUI with tkinter and topics data'
      replacements:
        'import pydoc_data.topics': "'raise ImportError'"
      change_function:
        'gui': 'un-callable'
        'cli': 'un-callable'
        'browse': 'un-callable'

- module-name: 'quopri' # checksum: 6a44b57d
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'main': "'(lambda: None)'"

- module-name: 'random' # checksum: 167cb032
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'site' # checksum: cf73d34
  data-files:
    - patterns:
        - 'orig-prefix.txt'

- module-name: 'sndhdr' # checksum: 820d21fd
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'test': "'(lambda: None)'"

- module-name: 'sysconfig' # checksum: f2059e78
  anti-bloat:
    - replacements:
        'osname, host, release, version, machine = os.uname()': "'osname, host, release, version, machine = os.uname(); osname = %r' % os.uname()[0]"
      when: 'not win32'

- module-name: 'tabnanny' # checksum: 6a44b57d
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'main': "'(lambda: None)'"

- module-name: 'tarfile' # checksum: 6a44b57d
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'main': "'(lambda: None)'"

- module-name: 'telnetlib' # checksum: 820d21fd
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'test': "'(lambda: None)'"

- module-name: 'threading' # checksum: 167cb032
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        '_test': "'(lambda: None)'"

- module-name: 'tkinter' # checksum: 481f7b98
  anti-bloat:
    - description: 'enhanced tk-inter in case of missing tcl'
      context:
        - 'import textwrap'
      replacements:
        'self.tk = _tkinter.create(screenName, baseName, className, interactive, wantobjects, useTk, sync, use)': |
          textwrap.indent("""
          try:
            self.tk = _tkinter.create(screenName, baseName, className, interactive, wantobjects, useTk, sync, use)
          except _tkinter.TclError as e:
            if "usable init.tcl" not in str(e):
              raise\n\
            sys.exit("Nuitka: Need to use '--enable-plugin=tk-inter' option during compilation for tk-inter to work!")
          """, "        ")
      when: 'not deployment and not plugin("tk-inter")'
  options:
    checks:
      - macos_bundle: 'recommend'
        when: 'plugin("tk-inter")'

- module-name: 'tokenize' # checksum: 6a44b57d
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'main': "'(lambda: None)'"

- module-name: 'trace' # checksum: 6a44b57d
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'main': "'(lambda: None)'"

- module-name: 'uu' # checksum: 820d21fd
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'test': "'(lambda: None)'"

- module-name: 'wsgiref.simple_server' # checksum: 24359f69
  implicit-imports:
    - depends:
        - 'http.server'

- module-name: 'xml.sax' # checksum: 82b0a965
  implicit-imports:
    - depends:
        - 'xml.sax.expatreader'

- module-name: 'xmlrpc.server' # checksum: 57da2566
  anti-bloat:
    - description: 'avoid using pydoc'
      replacements_plain:
        'import pydoc': ''
      change_class:
        'ServerHTMLDoc': 'un-usable'
      when: 'not use_pydoc'

- module-name: 'zipapp' # checksum: 6a44b57d
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'main': "'(lambda: None)'"

- module-name: 'zipfile' # checksum: 6a44b57d
  anti-bloat:
    - description: 'remove module ability to run as a binary'
      change_function:
        'main': "'(lambda: None)'"

- module-name: 'zoneinfo' # checksum: 70450440
  # This tries to find non-stdlib module when being used.
  implicit-imports:
    - depends:
        - 'tzdata.zoneinfo'
