#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Build Script - Lumma-Style Crypter
Mã hóa AES-256 + Anti-Detection
"""

import subprocess
import sys
import os

def main():
    print("🔥 Quick Lumma-Style Build")
    print("=" * 40)
    
    if not os.path.exists("main.py"):
        print("❌ main.py not found!")
        return False
    
    try:
        # Run the main build script
        result = subprocess.run([sys.executable, "build_final.py"], 
                              check=True, capture_output=False)
        return True
    except subprocess.CalledProcessError:
        print("❌ Build failed!")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Build completed! Check dist/ folder")
    else:
        print("\n❌ Build failed!")
    sys.exit(0 if success else 1)
